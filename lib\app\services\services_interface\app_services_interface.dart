import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Base interface for all application services
/// Defines the core contract that all services must implement
abstract class InitializableServiceAbstract extends GetxService {
  // ========================================
  // CORE LIFECYCLE METHODS
  // ========================================

  /// Initialize the service asynchronously
  /// This method should be called once during service setup
  Future<void> initialize();

  /// Ensure the service is initialized (idempotent operation)
  /// Safe to call multiple times - will only initialize once
  /// Returns the service instance for method chaining
  Future<dynamic> ensureInitialized();

  // ========================================
  // SERVICE STATE QUERIES
  // ========================================

  /// Check if the service has been initialized
  bool get isInitialized;

  /// Check if the service is ready for use
  /// May differ from isInitialized for services with complex startup
  bool get isReady;

  // ========================================
  // OPTIONAL LIFECYCLE HOOKS
  // ========================================

  /// Called when the service should start its operations
  /// Override in services that need explicit start/stop lifecycle
  Future<void> start();

  /// Called when the service should stop its operations
  /// Override in services that need cleanup
  Future<void> stop();

  /// Called to check if the service is healthy and functioning properly
  /// Override in services that need health monitoring
  bool get isHealthy;
}

/// Abstract base implementation for initializable services
///
/// This class provides common initialization logic and state management
/// for services. Concrete services should extend this class and implement
/// the `_init()` method with their specific initialization logic.
abstract class InitializableService extends GetxService
    implements InitializableServiceAbstract {
  // ========================================
  // PRIVATE STATE
  // ========================================

  bool _initialized = false;
  bool _started = false;
  Exception? _initializationError;

  // ========================================
  // ABSTRACT METHODS (must be implemented by subclasses)
  // ========================================

  /// Internal initialization method that subclasses must implement
  /// This method contains the actual service-specific initialization logic
  Future<void> _init();

  // ========================================
  // IMPLEMENTED INTERFACE METHODS
  // ========================================

  @override
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      await _init();
      _initialized = true;
      _initializationError = null;
    } catch (e) {
      _initialized = false;
      _initializationError = e is Exception ? e : Exception(e.toString());
      rethrow;
    }
  }

  @override
  Future<dynamic> ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
    return this;
  }

  @override
  bool get isInitialized => _initialized;

  @override
  bool get isReady => _initialized && _initializationError == null;

  // ========================================
  // DEFAULT IMPLEMENTATIONS (can be overridden)
  // ========================================

  @override
  Future<void> start() async {
    _started = true;
  }

  @override
  Future<void> stop() async {
    _started = false;
  }

  @override
  bool get isHealthy => isInitialized && _initializationError == null;

  // ========================================
  // ADDITIONAL UTILITY METHODS
  // ========================================

  /// Get the initialization error if any occurred
  Exception? get initializationError => _initializationError;

  /// Check if the service has been started
  bool get isStarted => _started;

  /// Reset the service state (for testing purposes)
  @protected
  void resetState() {
    _initialized = false;
    _started = false;
    _initializationError = null;
  }
}
