import 'package:deewan/app/data/models/entities/user_profile_model.dart';
import 'package:deewan/app/data/models/entities/item_models.dart';
import 'package:deewan/app/data/models/entities/message_models.dart';
import 'package:deewan/app/data/models/entities/contact_page_model.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class Identity {
  @Id()
  final int id;
  @Index() // Index for efficient lookups
  final String identityId; // Unique ID for this specific identity card in the system
  final String name;
  @Backlink('identity')
  final ToOne<Item>? item = ToOne<Item>();
  final String phoneNumber; //msg of phone number entity
  final String email; //email msg  entity
  final String status; //msg type
  @Property(type: PropertyType.date)
  final DateTime lastSeen;
  final String? type; // e.g., 'personal', 'work', 'incognito'
  final ToOne<ContactPage> contactPage = ToOne<ContactPage>();
  @Backlink('identity')
  final ToMany<RoomParticipant> roomParticipations = ToMany<RoomParticipant>();
  // @Backlink('blockedContacts')
  // final ToMany<MyIdentity> blockedContacts = ToMany<MyIdentity>();
  @Backlink('identity')
  final ToMany<ImageUrl>? imageUrl = ToMany<ImageUrl>();

  Identity(
    this.phoneNumber,
    this.email, {
    this.id = 0,
    required this.identityId,
    required this.name,
    this.status = '',
    required this.lastSeen,
    this.type,
  });
}

@Entity()
class MyIdentityCard {
  @Id()
  final int? id;
  final String? userName;
  final ToOne<Identity> identityId = ToOne<Identity>();
  final ToOne<UserPofile> userProfile = ToOne<UserPofile>();
  final String? title;
  @Backlink('roomId')
  final ToMany<Room>? rooms = ToMany<Room>();
  bool isDefault =
      false; // Whether this is the default identity for new interactions
  // @Backlink('itemList')
  // final ToMany<ItemList>? itemList = ToMany<ItemList>();
  final ToOne<ItemList>? bolckedContacts = ToOne<ItemList>(); //??
  final ToOne<ItemList>? contacts = ToOne<ItemList>();
  final ToOne<ItemList>? addresses = ToOne<ItemList>();

  @Backlink('myIdentity')
  final ToMany<ItemList>? medicalRecored = ToMany<ItemList>();

  MyIdentityCard(this.id, this.title, this.isDefault, this.userName);
}

@Entity()
class RoomParticipant {
  @Id()
  int id;
  @Property(type: PropertyType.date)
  DateTime joinDate;
  String? role; // e.g., 'member', 'admin'
  int unreadCount; // Per-identity unread count for this room
  @Property(type: PropertyType.date)
  DateTime? lastReadMessageTimestamp; // For per-identity read receipts

  // Relationships to IdentityCard and Room (Many-to-One)
  // Indexing these ToOne relationships' target IDs is crucial for queries.
  final ToOne<Identity> identity = ToOne<Identity>();
  final ToOne<Room> room = ToOne<Room>();

  RoomParticipant({
    this.id = 0,
    required this.joinDate,
    this.role,
    this.unreadCount = 0,
    this.lastReadMessageTimestamp,
  });
}
