import 'package:flutter/material.dart';
import 'app_services_interface.dart';

/// Interface for localization and internationalization services
abstract class ILocalizationService extends InitializableServiceAbstract {
  /// Current locale information
  Locale get currentLocale;
  String get currentLanguageCode;
  String? get currentCountryCode;

  /// Locale management
  Future<void> changeLocale(Locale locale);
  Future<void> toggleLanguage();
  Future<void> setLanguageByCode(String languageCode);

  /// Supported locales
  List<Locale> get supportedLocales;
  Locale get defaultLocale;
  Locale get fallbackLocale;

  /// Text direction and RTL support
  TextDirection get textDirection;
  bool get isRTL;
  bool get isLTR;
  bool get isEnglish;
  bool get isArabic;

  /// Language information
  String getLanguageName(Locale locale);
  String getLanguageDisplayName(Locale locale);
  String getLanguageNativeName(Locale locale);

  /// RTL-aware UI helpers
  TextAlign get textAlignStart;
  TextAlign get textAlignEnd;
  EdgeInsets getDirectionalPadding({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  });
  EdgeInsets getDirectionalMargin({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  });

  /// Locale persistence
  Future<void> saveLocalePreference(Locale locale);
  Future<Locale?> loadLocalePreference();
  Future<void> clearLocalePreference();

  /// Locale validation
  bool isLocaleSupported(Locale locale);
  Locale validateLocale(Locale locale);

  /// Date and number formatting
  String formatDate(DateTime date, {String? pattern});
  String formatNumber(num number, {int? decimalPlaces});
  String formatCurrency(double amount, {String? currencyCode});

  /// Pluralization support
  String getPlural(String key, int count, {Map<String, dynamic>? args});

  /// Translation utilities
  bool hasTranslation(String key);
  String getTranslation(String key, {Map<String, dynamic>? args});

  /// Locale change notifications
  Stream<Locale> get localeChanges;
  void addLocaleChangeListener(Function(Locale) listener);
  void removeLocaleChangeListener(Function(Locale) listener);
}
