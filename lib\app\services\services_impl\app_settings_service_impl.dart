import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/models/entities/app_settings_model.dart';
import '../services_interface/app_services_interface.dart';
import '../services_interface/app_settings_service_interface.dart';
import 'objectbox_service.dart';

/// App-global settings service implementation
/// Manages settings that apply to the entire application regardless of identity
class AppSettingsServiceImpl extends InitializableService
    implements AppSettingsServiceAbstract {
  
  // ========================================
  // PRIVATE FIELDS
  // ========================================
  
  late AppSettings _settings;
  
  // Stream controllers for change notifications
  final StreamController<AppSettings> _settingsController = StreamController<AppSettings>.broadcast();
  final StreamController<AppSettingsChangeEvent> _changeController = 
      StreamController<AppSettingsChangeEvent>.broadcast();
  final Map<String, List<Function(dynamic)>> _settingListeners = {};
  
  ObjectboxService get _objectboxService => Get.find<ObjectboxService>();

  // ========================================
  // SERVICE LIFECYCLE
  // ========================================

  @override
  Future<void> _init() async {
    await loadSettings();
  }

  @override
  bool get isHealthy => super.isHealthy && _objectboxService.isReady;

  Future<void> disposeResources() async {
    await _settingsController.close();
    await _changeController.close();
    _settingListeners.clear();
  }

  // ========================================
  // CORE SETTINGS OPERATIONS
  // ========================================

  @override
  Future<AppSettings> getSettings() async {
    return _settings;
  }

  @override
  Future<void> updateSettings(AppSettings settings) async {
    final oldSettings = _settings;
    _settings = settings;
    _settings.touch();
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('settings', oldSettings, _settings);
  }

  @override
  Stream<AppSettings> get settingsStream => _settingsController.stream;

  @override
  Future<void> loadSettings() async {
    try {
      _settings = _objectboxService.appSettingsBox?.get(1) ?? AppSettings.defaults();
    } catch (e) {
      _settings = AppSettings.defaults();
    }
  }

  @override
  Future<void> saveSettings() async {
    try {
      _settings.touch();
      _objectboxService.appSettingsBox?.put(_settings);
    } catch (e) {
      throw Exception('Failed to save app settings: $e');
    }
  }

  @override
  Future<void> resetSettings() async {
    _settings = AppSettings.defaults();
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('reset', null, _settings);
  }

  // ========================================
  // THEME AND APPEARANCE SETTINGS
  // ========================================

  @override
  bool get isDarkMode => _settings.isDarkMode ?? false;

  @override
  Future<void> setDarkMode(bool enabled) async {
    final oldValue = _settings.isDarkMode;
    _settings.isDarkMode = enabled;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('isDarkMode', oldValue, enabled);
  }

  @override
  ThemeMode get themeMode {
    if (_settings.useSystemTheme == true) return ThemeMode.system;
    return isDarkMode ? ThemeMode.dark : ThemeMode.light;
  }

  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    switch (mode) {
      case ThemeMode.light:
        await setDarkMode(false);
        await setUseSystemTheme(false);
        break;
      case ThemeMode.dark:
        await setDarkMode(true);
        await setUseSystemTheme(false);
        break;
      case ThemeMode.system:
        await setUseSystemTheme(true);
        break;
    }
  }

  @override
  double get fontSize => _settings.fontSize ?? 14.0;

  @override
  Future<void> setFontSize(double size) async {
    final oldValue = _settings.fontSize;
    _settings.fontSize = size;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('fontSize', oldValue, size);
  }

  @override
  String? get language => _settings.language;

  @override
  Future<void> setLanguage(String languageCode) async {
    final oldValue = _settings.language;
    _settings.language = languageCode;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('language', oldValue, languageCode);
  }

  @override
  String? get accentColor => _settings.accentColor;

  @override
  Future<void> setAccentColor(String? colorHex) async {
    final oldValue = _settings.accentColor;
    _settings.accentColor = colorHex;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('accentColor', oldValue, colorHex);
  }

  @override
  bool get useSystemTheme => _settings.useSystemTheme ?? true;

  @override
  Future<void> setUseSystemTheme(bool enabled) async {
    final oldValue = _settings.useSystemTheme;
    _settings.useSystemTheme = enabled;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('useSystemTheme', oldValue, enabled);
  }

  // ========================================
  // NOTIFICATION SETTINGS
  // ========================================

  @override
  bool get notificationsEnabled => _settings.notificationEnabled ?? true;

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    final oldValue = _settings.notificationEnabled;
    _settings.notificationEnabled = enabled;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('notificationsEnabled', oldValue, enabled);
  }

  @override
  bool get soundEnabled => _settings.soundEnabled ?? true;

  @override
  Future<void> setSoundEnabled(bool enabled) async {
    final oldValue = _settings.soundEnabled;
    _settings.soundEnabled = enabled;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('soundEnabled', oldValue, enabled);
  }

  @override
  bool get vibrationEnabled => _settings.vibrationEnabled ?? true;

  @override
  Future<void> setVibrationEnabled(bool enabled) async {
    final oldValue = _settings.vibrationEnabled;
    _settings.vibrationEnabled = enabled;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('vibrationEnabled', oldValue, enabled);
  }

  @override
  bool get showNotificationPreview => _settings.showNotificationPreview ?? true;

  @override
  Future<void> setShowNotificationPreview(bool enabled) async {
    final oldValue = _settings.showNotificationPreview;
    _settings.showNotificationPreview = enabled;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('showNotificationPreview', oldValue, enabled);
  }

  @override
  String? get quietHoursStart => _settings.quietHoursStart;

  @override
  Future<void> setQuietHoursStart(String? time) async {
    final oldValue = _settings.quietHoursStart;
    _settings.quietHoursStart = time;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('quietHoursStart', oldValue, time);
  }

  @override
  String? get quietHoursEnd => _settings.quietHoursEnd;

  @override
  Future<void> setQuietHoursEnd(String? time) async {
    final oldValue = _settings.quietHoursEnd;
    _settings.quietHoursEnd = time;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('quietHoursEnd', oldValue, time);
  }

  @override
  bool get weekendQuietMode => _settings.weekendQuietMode ?? false;

  @override
  Future<void> setWeekendQuietMode(bool enabled) async {
    final oldValue = _settings.weekendQuietMode;
    _settings.weekendQuietMode = enabled;
    await saveSettings();
    _settingsController.add(_settings);
    _notifyChange('weekendQuietMode', oldValue, enabled);
  }

  // ========================================
  // HELPER METHODS
  // ========================================

  void _notifyChange(String key, dynamic oldValue, dynamic newValue) {
    // Notify specific setting listeners
    _settingListeners[key]?.forEach((listener) => listener(newValue));
    
    // Notify change stream
    _changeController.add(AppSettingsChangeEvent(
      settingKey: key,
      oldValue: oldValue,
      newValue: newValue,
      timestamp: DateTime.now(),
    ));
  }

  @override
  Stream<AppSettingsChangeEvent> get settingsChanges => _changeController.stream;

  @override
  void addSettingListener(String key, Function(dynamic value) listener) {
    _settingListeners.putIfAbsent(key, () => []).add(listener);
  }

  @override
  void removeSettingListener(String key, Function(dynamic value) listener) {
    _settingListeners[key]?.remove(listener);
    if (_settingListeners[key]?.isEmpty == true) {
      _settingListeners.remove(key);
    }
  }

  // ========================================
  // REMAINING INTERFACE METHODS (SIMPLIFIED)
  // ========================================

  @override
  bool get analyticsEnabled => _settings.analyticsEnabled ?? true;

  @override
  Future<void> setAnalyticsEnabled(bool enabled) async {
    final oldValue = _settings.analyticsEnabled;
    _settings.analyticsEnabled = enabled;
    await saveSettings();
    _notifyChange('analyticsEnabled', oldValue, enabled);
  }

  @override
  bool get crashReportingEnabled => _settings.crashReportingEnabled ?? true;

  @override
  Future<void> setCrashReportingEnabled(bool enabled) async {
    final oldValue = _settings.crashReportingEnabled;
    _settings.crashReportingEnabled = enabled;
    await saveSettings();
    _notifyChange('crashReportingEnabled', oldValue, enabled);
  }

  @override
  bool get backgroundWorkEnabled => _settings.backgroundWorkEnabled ?? true;

  @override
  Future<void> setBackgroundWorkEnabled(bool enabled) async {
    final oldValue = _settings.backgroundWorkEnabled;
    _settings.backgroundWorkEnabled = enabled;
    await saveSettings();
    _notifyChange('backgroundWorkEnabled', oldValue, enabled);
  }

  // TODO: Implement remaining interface methods
  // This is a simplified implementation showing the pattern
  
  @override
  Future<String> exportSettings() async {
    return jsonEncode(_settings.toJson());
  }

  @override
  Future<bool> importSettings(String settingsData) async {
    try {
      final data = jsonDecode(settingsData);
      // TODO: Implement proper deserialization
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> validateSettings(String settingsData) async {
    try {
      jsonDecode(settingsData);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  AppSettingsMetadata get metadata => AppSettingsMetadata(
    lastModified: _settings.lastModified ?? DateTime.now(),
    createdAt: _settings.createdAt ?? DateTime.now(),
    version: _settings.settingsVersion ?? 1,
    deviceId: _settings.deviceName,
  );

  // Placeholder implementations for remaining getters/setters
  @override bool get shareUsageData => _settings.shareUsageData ?? false;
  @override Future<void> setShareUsageData(bool enabled) async => _notifyChange('shareUsageData', null, enabled);
  
  @override bool get personalizedAds => _settings.personalizedAds ?? false;
  @override Future<void> setPersonalizedAds(bool enabled) async => _notifyChange('personalizedAds', null, enabled);
  
  @override bool get enableTwoFactorAuth => _settings.enableTwoFactorAuth ?? false;
  @override Future<void> setEnableTwoFactorAuth(bool enabled) async => _notifyChange('enableTwoFactorAuth', null, enabled);
  
  @override bool get enableBiometricLogin => _settings.enableBiometricLogin ?? false;
  @override Future<void> setEnableBiometricLogin(bool enabled) async => _notifyChange('enableBiometricLogin', null, enabled);
  
  @override bool get autoLockEnabled => _settings.autoLockEnabled ?? false;
  @override Future<void> setAutoLockEnabled(bool enabled) async => _notifyChange('autoLockEnabled', null, enabled);
  
  @override int get autoLockTimeoutMinutes => _settings.autoLockTimeoutMinutes ?? 5;
  @override Future<void> setAutoLockTimeoutMinutes(int minutes) async => _notifyChange('autoLockTimeoutMinutes', null, minutes);
  
  @override bool get backgroundSyncEnabled => _settings.backgroundSyncEnabled ?? true;
  @override Future<void> setBackgroundSyncEnabled(bool enabled) async => _notifyChange('backgroundSyncEnabled', null, enabled);
  
  @override String get syncFrequency => _settings.syncFrequency ?? 'realtime';
  @override Future<void> setSyncFrequency(String frequency) async => _notifyChange('syncFrequency', null, frequency);
  
  @override bool get autoSaveEnabled => _settings.autoSaveEnabled ?? true;
  @override Future<void> setAutoSaveEnabled(bool enabled) async => _notifyChange('autoSaveEnabled', null, enabled);
  
  @override int get autoSaveIntervalMinutes => _settings.autoSaveIntervalMinutes ?? 5;
  @override Future<void> setAutoSaveIntervalMinutes(int minutes) async => _notifyChange('autoSaveIntervalMinutes', null, minutes);
  
  @override int get maxCacheSizeMB => _settings.maxCacheSizeMB ?? 500;
  @override Future<void> setMaxCacheSizeMB(int sizeMB) async => _notifyChange('maxCacheSizeMB', null, sizeMB);
  
  @override String get dataUsagePreference => _settings.dataUsagePreference ?? 'medium';
  @override Future<void> setDataUsagePreference(String preference) async => _notifyChange('dataUsagePreference', null, preference);
  
  @override bool get wifiOnlySync => _settings.wifiOnlySync ?? false;
  @override Future<void> setWifiOnlySync(bool enabled) async => _notifyChange('wifiOnlySync', null, enabled);
  
  @override bool get roamingDataEnabled => _settings.roamingDataEnabled ?? false;
  @override Future<void> setRoamingDataEnabled(bool enabled) async => _notifyChange('roamingDataEnabled', null, enabled);
  
  @override bool get highContrastMode => _settings.highContrastMode ?? false;
  @override Future<void> setHighContrastMode(bool enabled) async => _notifyChange('highContrastMode', null, enabled);
  
  @override bool get largeTextMode => _settings.largeTextMode ?? false;
  @override Future<void> setLargeTextMode(bool enabled) async => _notifyChange('largeTextMode', null, enabled);
  
  @override bool get screenReaderEnabled => _settings.screenReaderEnabled ?? false;
  @override Future<void> setScreenReaderEnabled(bool enabled) async => _notifyChange('screenReaderEnabled', null, enabled);
  
  @override bool get reduceMotion => _settings.reduceMotion ?? false;
  @override Future<void> setReduceMotion(bool enabled) async => _notifyChange('reduceMotion', null, enabled);
  
  @override bool get hapticFeedbackEnabled => _settings.hapticFeedbackEnabled ?? true;
  @override Future<void> setHapticFeedbackEnabled(bool enabled) async => _notifyChange('hapticFeedbackEnabled', null, enabled);
  
  @override String? get deviceName => _settings.deviceName;
  @override Future<void> setDeviceName(String? name) async => _notifyChange('deviceName', null, name);
  
  @override String? get passCodeLock => _settings.passCodeLock;
  @override Future<void> setPassCodeLock(String? code) async => _notifyChange('passCodeLock', null, code);
}
