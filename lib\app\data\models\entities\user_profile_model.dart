import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/app/data/models/entities/message_models.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class UserPofile {
  @Id()
  final String? id;
  final String userProfileId;
  final String? title;
  //final ToMany<Device>? registeredDeviceIds = ToMany<Device>();
  final String? email;
  final String? phone;
  final String? pinCode;
  final bool? isVerified;
  @Backlink('imageUrl')
  final ToMany<ImageUrl>? circleAvatar = ToMany<ImageUrl>();
  @Backlink('userProfile')
  final ToMany<MyIdentityCard>? myIdentity = ToMany<MyIdentityCard>();
  final DateTime createdAt;
  final bool emailVerified;
  final bool phoneVerified;

  UserPofile(
    this.id,
    this.email,
    this.emailVerified,
    this.phoneVerified,
    this.phone,
    this.pinCode,
    this.userProfileId,
    this.title,
    this.isVerified,
    this.createdAt,
  );
}
