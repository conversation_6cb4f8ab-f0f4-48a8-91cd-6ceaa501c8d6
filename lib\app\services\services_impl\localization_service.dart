import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/models/entities/sittings_model.dart';
import '../services_interface/app_services_interface.dart';
import '../services_interface/localization_service_interface.dart';
import 'objectbox_service.dart';

/// Comprehensive localization service that manages language switching,
/// RTL support, and integrates with ObjectBox storage
class LocalizationService extends InitializableService
    implements LocalizationServiceAbstract {
  static LocalizationService get to => Get.find();

  // Supported locales
  static const List<Locale> _supportedLocales = [
    Locale('en', 'US'), // English (United States)
    Locale('ar', 'SA'), // Arabic (Arabia)
  ];

  // Default locale
  static const Locale _defaultLocale = Locale('en', 'US');
  static const Locale _fallbackLocale = Locale('ar', 'SA');

  // Current locale reactive variable
  final Rx<Locale> _currentLocale = _defaultLocale.obs;

  // Text direction reactive variable
  final Rx<TextDirection> _textDirection = TextDirection.ltr.obs;

  // Locale change stream
  final StreamController<Locale> _localeChangeController =
      StreamController<Locale>.broadcast();
  final List<Function(Locale)> _localeChangeListeners = [];

  // Interface getters
  @override
  Locale get currentLocale => _currentLocale.value;

  @override
  TextDirection get textDirection => _textDirection.value;

  @override
  List<Locale> get supportedLocales => List.unmodifiable(_supportedLocales);

  @override
  Locale get defaultLocale => _defaultLocale;

  @override
  Locale get fallbackLocale => _fallbackLocale;

  @override
  String get currentLanguageCode => _currentLocale.value.languageCode;

  @override
  String? get currentCountryCode => _currentLocale.value.countryCode;

  @override
  bool get isRTL => _isRTL(_currentLocale.value);

  @override
  bool get isLTR => !isRTL;

  @override
  bool get isEnglish => _currentLocale.value.languageCode == 'en';

  @override
  bool get isArabic => _currentLocale.value.languageCode == 'ar';

  @override
  TextAlign get textAlignStart => isRTL ? TextAlign.right : TextAlign.left;

  @override
  TextAlign get textAlignEnd => isRTL ? TextAlign.left : TextAlign.right;

  @override
  Stream<Locale> get localeChanges => _localeChangeController.stream;

  // Language names for UI display
  static const Map<String, String> languageNames = {
    'en_US': 'English',
    'ar_SA': 'العربية',
  };

  // ObjectBox service for persistence
  late ObjectboxService _objectboxService;
  late Settings _settings;

  @override
  Future<void> _init() async {
    _objectboxService = Get.find<ObjectboxService>();
    await _initializeLocale();
  }

  @override
  Future<void> start() async {
    await super.start();
    // Additional start logic if needed
  }

  @override
  Future<void> stop() async {
    await super.stop();
    // Additional stop logic if needed
  }

  @override
  bool get isHealthy => super.isHealthy && _objectboxService.isReady;

  /// Initialize locale from stored settings or device locale
  Future<void> _initializeLocale() async {
    try {
      // Try to get stored settings
      _settings = _objectboxService.appSettingsBox?.get(1) ?? Settings(id: 1);

      Locale? storedLocale;
      if (_settings.language != null) {
        storedLocale = _parseLocaleFromString(_settings.language!);
      }

      // Use stored locale, device locale, or default locale
      Locale initialLocale =
          storedLocale ?? _getDeviceLocale() ?? defaultLocale;

      await _changeLocaleInternal(initialLocale, saveToStorage: false);
    } catch (e) {
      print('Error initializing locale: $e');
      await _changeLocaleInternal(defaultLocale, saveToStorage: false);
    }
  }

  /// Get device locale if supported, otherwise return null
  Locale? _getDeviceLocale() {
    final deviceLocale = Get.deviceLocale;
    if (deviceLocale != null) {
      // Check if device locale is supported
      for (final supportedLocale in supportedLocales) {
        if (supportedLocale.languageCode == deviceLocale.languageCode) {
          return supportedLocale;
        }
      }
    }
    return null;
  }

  /// Parse locale from string format (e.g., "en_US" or "ar_SA")
  Locale _parseLocaleFromString(String localeString) {
    final parts = localeString.split('_');
    if (parts.length == 2) {
      return Locale(parts[0], parts[1]);
    } else {
      // Handle legacy format (e.g., "en" or "ar")
      switch (parts[0]) {
        case 'en':
          return const Locale('en', 'US');
        case 'ar':
          return const Locale('ar', 'SA');
        default:
          return defaultLocale;
      }
    }
  }

  /// Convert locale to string format for storage
  String _localeToString(Locale locale) {
    return '${locale.languageCode}_${locale.countryCode}';
  }

  /// Change the current locale
  @override
  Future<void> changeLocale(Locale newLocale) async {
    await _changeLocaleInternal(newLocale, saveToStorage: true);
  }

  /// Internal method to change locale with optional save to storage
  Future<void> _changeLocaleInternal(
    Locale newLocale, {
    bool saveToStorage = true,
  }) async {
    try {
      // Validate locale is supported
      if (!supportedLocales.contains(newLocale)) {
        print('Unsupported locale: $newLocale');
        return;
      }

      // Update current locale
      _currentLocale.value = newLocale;

      // Update text direction based on language
      _textDirection.value = _isRTL(newLocale)
          ? TextDirection.rtl
          : TextDirection.ltr;

      // Update GetX locale
      Get.updateLocale(newLocale);

      // Save to storage if requested
      if (saveToStorage) {
        await _saveLocaleToStorage(newLocale);
      }

      // Notify listeners
      for (final listener in _localeChangeListeners) {
        listener(newLocale);
      }

      // Emit to stream
      _localeChangeController.add(newLocale);

      print('Locale changed to: ${_localeToString(newLocale)}');
    } catch (e) {
      print('Error changing locale: $e');
    }
  }

  /// Save locale to ObjectBox storage
  Future<void> _saveLocaleToStorage(Locale locale) async {
    try {
      _settings.language = _localeToString(locale);
      _objectboxService.appSettingsBox?.put(_settings);
      print('Locale saved to storage: ${_settings.language}');
    } catch (e) {
      print('Error saving locale to storage: $e');
    }
  }

  /// Check if locale uses RTL text direction
  bool _isRTL(Locale locale) {
    return locale.languageCode == 'ar';
  }

  /// Get current locale string
  String get currentLocaleString => _localeToString(_currentLocale.value);

  /// Get display name for current language
  String get currentLanguageName =>
      languageNames[currentLocaleString] ?? currentLanguageCode;

  /// Get display name for a specific locale
  @override
  String getLanguageName(Locale locale) {
    return languageNames[_localeToString(locale)] ?? locale.languageCode;
  }

  /// Toggle between supported languages
  @override
  Future<void> toggleLanguage() async {
    if (isEnglish) {
      await changeLocale(const Locale('ar', 'SA'));
    } else {
      await changeLocale(const Locale('en', 'US'));
    }
  }

  /// Get list of supported locales for UI
  List<Locale> get availableLocales => List.unmodifiable(supportedLocales);

  /// Get locale-specific text alignment
  TextAlign get textAlign => isRTL ? TextAlign.right : TextAlign.left;

  /// Get locale-specific edge insets (for RTL support)
  @override
  EdgeInsets getDirectionalPadding({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return isRTL
        ? EdgeInsets.only(left: end, top: top, right: start, bottom: bottom)
        : EdgeInsets.only(left: start, top: top, right: end, bottom: bottom);
  }

  /// Get locale-specific margin
  @override
  EdgeInsets getDirectionalMargin({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return getDirectionalPadding(
      start: start,
      top: top,
      end: end,
      bottom: bottom,
    );
  }

  /// Format numbers according to locale
  @override
  String formatNumber(num number, {int? decimalPlaces}) {
    // For Arabic, you might want to use Arabic-Indic digits
    if (isArabic) {
      // Convert to Arabic-Indic numerals if needed
      return number.toString();
    }
    return number.toString();
  }

  // Additional interface methods
  @override
  Future<void> setLanguageByCode(String languageCode) async {
    Locale? targetLocale;
    for (final locale in supportedLocales) {
      if (locale.languageCode == languageCode) {
        targetLocale = locale;
        break;
      }
    }
    if (targetLocale != null) {
      await changeLocale(targetLocale);
    }
  }

  @override
  String getLanguageDisplayName(Locale locale) {
    return getLanguageName(locale);
  }

  @override
  String getLanguageNativeName(Locale locale) {
    return getLanguageName(locale);
  }

  @override
  Future<void> saveLocalePreference(Locale locale) async {
    await _saveLocaleToStorage(locale);
  }

  @override
  Future<Locale?> loadLocalePreference() async {
    try {
      final settings = _objectboxService.appSettingsBox?.get(1);
      if (settings?.language != null) {
        return _parseLocaleFromString(settings!.language!);
      }
    } catch (e) {
      print('Error loading locale preference: $e');
    }
    return null;
  }

  @override
  Future<void> clearLocalePreference() async {
    try {
      _settings.language = null;
      _objectboxService.appSettingsBox?.put(_settings);
    } catch (e) {
      print('Error clearing locale preference: $e');
    }
  }

  @override
  bool isLocaleSupported(Locale locale) {
    return supportedLocales.contains(locale);
  }

  @override
  Locale validateLocale(Locale locale) {
    return isLocaleSupported(locale) ? locale : defaultLocale;
  }

  @override
  String formatDate(DateTime date, {String? pattern}) {
    // Basic date formatting - can be enhanced with intl package
    return date.toString();
  }

  @override
  String formatCurrency(double amount, {String? currencyCode}) {
    // Basic currency formatting - can be enhanced with intl package
    return amount.toString();
  }

  @override
  String getPlural(String key, int count, {Map<String, dynamic>? args}) {
    // Basic pluralization - can be enhanced with proper pluralization rules
    return key;
  }

  @override
  bool hasTranslation(String key) {
    // Basic implementation - should check actual translation keys
    return true;
  }

  @override
  String getTranslation(String key, {Map<String, dynamic>? args}) {
    // Basic implementation - should return actual translations
    return key;
  }

  @override
  void addLocaleChangeListener(Function(Locale) listener) {
    _localeChangeListeners.add(listener);
  }

  @override
  void removeLocaleChangeListener(Function(Locale) listener) {
    _localeChangeListeners.remove(listener);
  }

  /// Get appropriate font family for current locale
  String? get fontFamily {
    if (isArabic) {
      // Return Arabic font family if you have one
      return null; // Use system default for now
    }
    return null; // Use system default
  }

  /// Dispose resources
  @override
  void onClose() {
    _localeChangeController.close();
    super.onClose();
  }
}
