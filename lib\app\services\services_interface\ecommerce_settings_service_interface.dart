import 'app_services_interface.dart';

/// Interface for e-commerce specific settings
abstract class EcommerceSettingsServiceAbstract extends InitializableServiceAbstract {
  // ========================================
  // PAYMENT SETTINGS
  // ========================================
  
  String? get defaultPaymentMethod;
  Future<void> setDefaultPaymentMethod(String? method);
  
  bool get savePaymentMethods;
  Future<void> setSavePaymentMethods(bool enabled);
  
  bool get requireBiometricForPayments;
  Future<void> setRequireBiometricForPayments(bool enabled);
  
  String get preferredCurrency;
  Future<void> setPreferredCurrency(String currency);
  
  // ========================================
  // SHIPPING & DELIVERY SETTINGS
  // ========================================
  
  String? get defaultShippingAddress;
  Future<void> setDefaultShippingAddress(String? address);
  
  bool get saveShippingAddresses;
  Future<void> setSaveShippingAddresses(bool enabled);
  
  String get preferredDeliveryTime; // 'morning', 'afternoon', 'evening'
  Future<void> setPreferredDeliveryTime(String time);
  
  bool get enableOrderTracking;
  Future<void> setEnableOrderTracking(bool enabled);
  
  // ========================================
  // PRODUCT & SHOPPING PREFERENCES
  // ========================================
  
  bool get showPriceAlerts;
  Future<void> setShowPriceAlerts(bool enabled);
  
  bool get enableWishlistSync;
  Future<void> setEnableWishlistSync(bool enabled);
  
  String get productSortPreference; // 'price_low', 'price_high', 'rating', 'newest'
  Future<void> setProductSortPreference(String preference);
  
  bool get showOutOfStockItems;
  Future<void> setShowOutOfStockItems(bool enabled);
  
  // ========================================
  // NOTIFICATION SETTINGS
  // ========================================
  
  bool get orderNotificationsEnabled;
  Future<void> setOrderNotificationsEnabled(bool enabled);
  
  bool get paymentNotificationsEnabled;
  Future<void> setPaymentNotificationsEnabled(bool enabled);
  
  bool get promotionNotificationsEnabled;
  Future<void> setPromotionNotificationsEnabled(bool enabled);
  
  bool get deliveryNotificationsEnabled;
  Future<void> setDeliveryNotificationsEnabled(bool enabled);
  
  // ========================================
  // PERSONALIZATION SETTINGS
  // ========================================
  
  bool get personalizedRecommendations;
  Future<void> setPersonalizedRecommendations(bool enabled);
  
  bool get showRecentlyViewed;
  Future<void> setShowRecentlyViewed(bool enabled);
  
  List<String> get favoriteCategories;
  Future<void> setFavoriteCategories(List<String> categories);
  
  // ========================================
  // SECURITY & PRIVACY SETTINGS
  // ========================================
  
  bool get saveOrderHistory;
  Future<void> setSaveOrderHistory(bool enabled);
  
  bool get sharePurchaseData;
  Future<void> setSharePurchaseData(bool enabled);
  
  bool get enablePurchaseVerification;
  Future<void> setEnablePurchaseVerification(bool enabled);
  
  // ========================================
  // SETTINGS MANAGEMENT
  // ========================================
  
  Stream<EcommerceSettingsChange> get settingsChanges;
  
  /// Reset all e-commerce settings to defaults
  Future<void> resetToDefaults();
  
  /// Export e-commerce settings
  Future<Map<String, dynamic>> exportSettings();
  
  /// Import e-commerce settings
  Future<bool> importSettings(Map<String, dynamic> settings);
  
  /// Validate payment method
  Future<bool> validatePaymentMethod(String method);
  
  /// Validate shipping address
  Future<bool> validateShippingAddress(String address);
  
  /// Get supported currencies
  Future<List<String>> getSupportedCurrencies();
  
  /// Get available payment methods
  Future<List<PaymentMethod>> getAvailablePaymentMethods();
}

/// E-commerce settings change event
class EcommerceSettingsChange {
  final String settingKey;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;
  
  EcommerceSettingsChange({
    required this.settingKey,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
  });
}

/// Payment method information
class PaymentMethod {
  final String id;
  final String name;
  final String type; // 'card', 'wallet', 'bank', 'crypto'
  final String? icon;
  final bool isEnabled;
  final Map<String, dynamic>? metadata;
  
  PaymentMethod({
    required this.id,
    required this.name,
    required this.type,
    this.icon,
    this.isEnabled = true,
    this.metadata,
  });
}

/// Product sort preferences
enum ProductSortPreference {
  priceLow,
  priceHigh,
  rating,
  newest,
  popularity,
  relevance,
}

/// Delivery time preferences
enum DeliveryTimePreference {
  morning,
  afternoon,
  evening,
  anytime,
}

/// Order notification types
enum OrderNotificationType {
  all,
  statusUpdatesOnly,
  deliveryOnly,
  none,
}
