import 'app_services_interface.dart';

/// Interface for service initialization and lifecycle management
///
/// This interface focuses solely on the initialization process and lifecycle
/// management of services, following the single responsibility principle.
abstract class IServiceInitializer {
  // ========================================
  // SERVICE INITIALIZATION
  // ========================================

  /// Initialize all application services in the correct dependency order
  Future<void> initializeAllServices();

  /// Initialize a specific service by type
  Future<T> initializeService<T extends InitializableServiceAbstract>();

  /// Get the order in which services should be initialized
  /// Services with dependencies should come after their dependencies
  List<Type> get initializationOrder;

  // ========================================
  // INITIALIZATION STATUS
  // ========================================

  /// Check if all services have been successfully initialized
  bool get allServicesInitialized;

  /// Check if a specific service type has been initialized
  bool isServiceInitialized<T extends InitializableServiceAbstract>();

  /// Get list of services that failed to initialize
  List<Type> get failedServices;

  // ========================================
  // SERVICE LIFECYCLE MANAGEMENT
  // ========================================

  /// Start all initialized services
  Future<void> startServices();

  /// Stop all running services (in reverse dependency order)
  Future<void> stopServices();

  /// Restart all services (stop then start)
  Future<void> restartServices();

  // ========================================
  // DEPENDENCY MANAGEMENT
  // ========================================

  /// Get the list of dependencies for a specific service type
  List<Type> getServiceDependencies<T extends InitializableServiceAbstract>();

  /// Check if all dependencies for a service are satisfied
  bool areDependenciesSatisfied<T extends InitializableServiceAbstract>();

  // ========================================
  // ERROR HANDLING
  // ========================================

  /// Handle service initialization errors
  void onServiceInitializationError<T extends InitializableServiceAbstract>(
    Exception error,
  );

  /// Get all initialization errors that have occurred
  List<Exception> get initializationErrors;

  /// Clear all recorded initialization errors
  void clearErrors();
}

// Note: Service registry and health monitoring interfaces have been moved
// to separate files for better organization:
// - IServiceRegistry -> service_registry_interface.dart
// - IServiceHealthMonitor -> service_health_monitor_interface.dart
