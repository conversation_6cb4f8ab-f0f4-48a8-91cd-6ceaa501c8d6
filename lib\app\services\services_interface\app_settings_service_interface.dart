import 'package:flutter/material.dart';
import 'app_services_interface.dart';
import '../../data/models/entities/app_settings_model.dart';

/// Interface for app-global settings management
/// Handles settings that apply to the entire application regardless of identity
abstract class AppSettingsServiceAbstract extends InitializableServiceAbstract {
  // ========================================
  // CORE APP SETTINGS OPERATIONS
  // ========================================

  /// Get current app-global settings
  Future<AppSettings> getSettings();

  /// Update app-global settings
  Future<void> updateSettings(AppSettings settings);

  /// Stream of app-global settings changes
  Stream<AppSettings> get settingsStream;

  /// Load app-global settings from persistent storage
  Future<void> loadSettings();

  /// Save app-global settings to persistent storage
  Future<void> saveSettings();

  /// Reset app-global settings to default values
  Future<void> resetSettings();

  // ========================================
  // THEME AND APPEARANCE SETTINGS
  // ========================================

  bool get isDarkMode;
  Future<void> setDarkMode(bool enabled);

  ThemeMode get themeMode;
  Future<void> setThemeMode(ThemeMode mode);

  double get fontSize;
  Future<void> setFontSize(double size);

  String? get language;
  Future<void> setLanguage(String languageCode);

  String? get accentColor;
  Future<void> setAccentColor(String? colorHex);

  bool get useSystemTheme;
  Future<void> setUseSystemTheme(bool enabled);

  // ========================================
  // GLOBAL NOTIFICATION SETTINGS
  // ========================================

  bool get notificationsEnabled;
  Future<void> setNotificationsEnabled(bool enabled);

  bool get soundEnabled;
  Future<void> setSoundEnabled(bool enabled);

  bool get vibrationEnabled;
  Future<void> setVibrationEnabled(bool enabled);

  bool get showNotificationPreview;
  Future<void> setShowNotificationPreview(bool enabled);

  // Quiet hours
  String? get quietHoursStart;
  Future<void> setQuietHoursStart(String? time);

  String? get quietHoursEnd;
  Future<void> setQuietHoursEnd(String? time);

  bool get weekendQuietMode;
  Future<void> setWeekendQuietMode(bool enabled);

  // ========================================
  // PRIVACY AND SECURITY SETTINGS
  // ========================================

  bool get analyticsEnabled;
  Future<void> setAnalyticsEnabled(bool enabled);

  bool get crashReportingEnabled;
  Future<void> setCrashReportingEnabled(bool enabled);

  bool get shareUsageData;
  Future<void> setShareUsageData(bool enabled);

  bool get personalizedAds;
  Future<void> setPersonalizedAds(bool enabled);

  // Security
  bool get enableTwoFactorAuth;
  Future<void> setEnableTwoFactorAuth(bool enabled);

  bool get enableBiometricLogin;
  Future<void> setEnableBiometricLogin(bool enabled);

  bool get autoLockEnabled;
  Future<void> setAutoLockEnabled(bool enabled);

  int get autoLockTimeoutMinutes;
  Future<void> setAutoLockTimeoutMinutes(int minutes);

  // ========================================
  // SYSTEM AND PERFORMANCE SETTINGS
  // ========================================

  bool get backgroundWorkEnabled;
  Future<void> setBackgroundWorkEnabled(bool enabled);

  bool get backgroundSyncEnabled;
  Future<void> setBackgroundSyncEnabled(bool enabled);

  String get syncFrequency;
  Future<void> setSyncFrequency(String frequency);

  bool get autoSaveEnabled;
  Future<void> setAutoSaveEnabled(bool enabled);

  int get autoSaveIntervalMinutes;
  Future<void> setAutoSaveIntervalMinutes(int minutes);

  int get maxCacheSizeMB;
  Future<void> setMaxCacheSizeMB(int sizeMB);

  String get dataUsagePreference;
  Future<void> setDataUsagePreference(String preference);

  bool get wifiOnlySync;
  Future<void> setWifiOnlySync(bool enabled);

  bool get roamingDataEnabled;
  Future<void> setRoamingDataEnabled(bool enabled);

  // ========================================
  // ACCESSIBILITY SETTINGS
  // ========================================

  bool get highContrastMode;
  Future<void> setHighContrastMode(bool enabled);

  bool get largeTextMode;
  Future<void> setLargeTextMode(bool enabled);

  bool get screenReaderEnabled;
  Future<void> setScreenReaderEnabled(bool enabled);

  bool get reduceMotion;
  Future<void> setReduceMotion(bool enabled);

  bool get hapticFeedbackEnabled;
  Future<void> setHapticFeedbackEnabled(bool enabled);

  // ========================================
  // DEVICE SETTINGS
  // ========================================

  String? get deviceName;
  Future<void> setDeviceName(String? name);

  String? get passCodeLock;
  Future<void> setPassCodeLock(String? code);

  // ========================================
  // SETTINGS MANAGEMENT
  // ========================================

  /// Export app settings
  Future<String> exportSettings();

  /// Import app settings
  Future<bool> importSettings(String settingsData);

  /// Validate settings data
  Future<bool> validateSettings(String settingsData);

  /// Get settings metadata
  AppSettingsMetadata get metadata;

  /// Stream of setting changes
  Stream<AppSettingsChangeEvent> get settingsChanges;

  /// Add listener for specific setting changes
  void addSettingListener(String key, Function(dynamic value) listener);

  /// Remove setting listener
  void removeSettingListener(String key, Function(dynamic value) listener);
}

// ========================================
// SUPPORTING CLASSES
// ========================================

/// App settings change event
class AppSettingsChangeEvent {
  final String settingKey;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;

  AppSettingsChangeEvent({
    required this.settingKey,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
  });
}

/// App settings metadata
class AppSettingsMetadata {
  final DateTime lastModified;
  final DateTime createdAt;
  final int version;
  final String? deviceId;

  AppSettingsMetadata({
    required this.lastModified,
    required this.createdAt,
    required this.version,
    this.deviceId,
  });
}

/// Sync frequency options
enum SyncFrequency {
  realtime,
  hourly,
  daily,
  manual,
}

/// Data usage preferences
enum DataUsagePreference {
  low,
  medium,
  high,
}
