// websocket_service.dart
abstract class WebSocketService {
  Future<void> initialize();
  Future<bool> connect();
  Future<void> disconnect();
  Future<void> reconnect();
  bool get isConnected;
  Stream<ConnectionStatus> get connectionStatusStream;
  Stream<WebSocketEvent> get eventStream;
  Future<void> sendMessage(WebSocketMessage message);
  Future<void> joinRoom(String roomId);
  Future<void> leaveRoom(String roomId);
  Future<void> sendTypingIndicator(String roomId, bool isTyping);
  Future<void> sendReadReceipt(String messageId);
  Future<void> sendPresenceUpdate(PresenceStatus status);
  void dispose();
}

// authentication_service.dart
abstract class AuthenticationService {
  Future<void> initialize();
  Future<AuthResult> login(String email, String password);
  Future<AuthResult> register(String email, String password, String phone);
  Future<AuthResult> loginWithPhone(String phoneNumber);
  Future<AuthResult> verifyPhoneOtp(String otp);
  Future<AuthResult> loginWithBiometrics();
  Future<bool> logout();
  Future<bool> refreshToken();
  Future<UserProfile?> getCurrentUser();
  Stream<AuthStatus> get authStatusStream;
  bool get isAuthenticated;
  Future<bool> resetPassword(String email);
  Future<bool> changePassword(String oldPassword, String newPassword);
  Future<bool> deleteAccount();
  Future<void> clearAuthData();
}

// message_service.dart
abstract class MessageService {
  Future<void> initialize();
  Future<Message> sendTextMessage(String roomId, String content);
  Future<Message> sendMediaMessage(String roomId, String filePath, MessageType type);
  Future<Message> sendLocationMessage(String roomId, double latitude, double longitude);
  Future<Message> sendContactMessage(String roomId, String contactId);
  Future<Message> editMessage(String messageId, String newContent);
  Future<bool> deleteMessage(String messageId, bool deleteForEveryone);
  Future<bool> forwardMessage(String messageId, List<String> roomIds);
  Future<bool> starMessage(String messageId);
  Future<bool> unstarMessage(String messageId);
  Future<void> markMessageAsRead(String messageId);
  Future<void> markRoomAsRead(String roomId);
  Stream<List<Message>> getMessagesStream(String roomId);
  Future<List<Message>> searchMessages(String query, {String? roomId});
  Future<void> retryFailedMessages();
  void dispose();
}

// notification_service.dart
abstract class NotificationService {
  Future<void> initialize();
  Future<bool> requestPermissions();
  Future<String?> getDeviceToken();
  Future<void> showLocalNotification(LocalNotification notification);
  Future<void> scheduleNotification(LocalNotification notification, DateTime scheduledTime);
  Future<void> cancelNotification(String notificationId);
  Future<void> cancelAllNotifications();
  Future<void> setBadgeCount(int count);
  Future<void> clearBadge();
  Stream<NotificationResponse> get notificationResponseStream;
  Future<void> handleForegroundNotification(RemoteMessage message);
  Future<void> handleBackgroundNotification(RemoteMessage message);
  Future<void> updateNotificationSettings(NotificationSettings settings);
  void dispose();
}

// room_service.dart
abstract class RoomService {
  Future<void> initialize();
  Future<Room> createPrivateRoom(String participantId);
  Future<Room> createGroupRoom(String title, List<String> participantIds);
  Future<Room> createChannelRoom(String title, String? description);
  Future<Room> createOrderRoom(String title, String category);
  Future<void> joinRoom(String roomId);
  Future<void> leaveRoom(String roomId);
  Future<void> addParticipant(String roomId, String participantId);
  Future<void> removeParticipant(String roomId, String participantId);
  Future<void> updateRoomInfo(String roomId, String? title, String? description);
  Future<void> archiveRoom(String roomId);
  Future<void> unarchiveRoom(String roomId);
  Future<void> muteRoom(String roomId, Duration? duration);
  Future<void> unmuteRoom(String roomId);
  Future<void> pinRoom(String roomId);
  Future<void> unpinRoom(String roomId);
  Stream<List<Room>> getRoomsStream();
  Future<List<RoomParticipant>> getRoomParticipants(String roomId);
  void dispose();
}

// sync_service.dart
abstract class SyncService {
  Future<void> initialize();
  Future<void> startSync();
  Future<void> stopSync();
  Future<void> syncNow();
  Future<void> syncRoom(String roomId);
  Future<void> syncMessages(String roomId, {DateTime? since});
  Future<void> syncUserProfile();
  Future<void> syncContacts();
  Stream<SyncStatus> get syncStatusStream;
  Future<void> handleIncomingMessage(Map<String, dynamic> messageData);
  Future<void> handleRoomUpdate(Map<String, dynamic> roomData);
  Future<void> handleUserUpdate(Map<String, dynamic> userData);
  Future<void> schedulePeriodicSync(Duration interval);
  void dispose();
}

// offline_queue_service.dart
abstract class OfflineQueueService {
  Future<void> initialize();
  Future<void> enqueueMessage(Message message);
  Future<void> enqueueRoomAction(RoomAction action);
  Future<void> enqueueUserAction(UserAction action);
  Future<void> processQueue();
  Future<void> retryFailedItems();
  Future<void> clearQueue();
  Stream<QueueStatus> get queueStatusStream;
  Future<List<QueueItem>> getPendingItems();
  Future<void> removeQueueItem(String itemId);
  Future<void> pauseQueue();
  Future<void> resumeQueue();
  void dispose();
}

// network_service.dart
abstract class NetworkService {
  Future<void> initialize();
  Stream<NetworkStatus> get networkStatusStream;
  Future<bool> isConnected();
  Future<bool> hasInternetAccess();
  NetworkStatus get currentStatus;
  Future<void> startMonitoring();
  Future<void> stopMonitoring();
  Future<NetworkQuality> testNetworkQuality();
  Future<void> handleNetworkChange(NetworkStatus status);
  void dispose();
}

// file_service.dart
abstract class FileService {
  Future<void> initialize();
  Future<String?> pickFile(FileType type);
  Future<List<String>> pickMultipleFiles(FileType type);
  Future<String?> captureImage();
  Future<String?> captureVideo();
  Future<String?> recordAudio(Duration maxDuration);
  Future<String?> compressImage(String imagePath, {int quality = 80});
  Future<String?> compressVideo(String videoPath);
  Future<String?> generateThumbnail(String videoPath);
  Future<String> uploadFile(String filePath, FileType type);
  Future<String?> downloadFile(String url, String fileName);
  Future<bool> deleteFile(String filePath);
  Future<int> getFileSize(String filePath);
  Future<String> getFileHash(String filePath);
  void dispose();
}

// encryption_service.dart
abstract class EncryptionService {
  Future<void> initialize();
  Future<KeyPair> generateKeyPair();
  Future<String> encrypt(String plainText, String publicKey);
  Future<String> decrypt(String cipherText, String privateKey);
  Future<String> encryptMessage(String message, String roomId);
  Future<String> decryptMessage(String encryptedMessage, String roomId);
  Future<String> hashPassword(String password);
  Future<bool> verifyPassword(String password, String hash);
  Future<String> generateSecureToken();
  Future<String> generateRoomKey();
  Future<void> rotateRoomKey(String roomId);
  Future<void> exportKeys(String password);
  Future<void> importKeys(String keyData, String password);
  void dispose();
}

// media_service.dart
abstract class MediaService {
  Future<void> initialize();
  Future<String?> processImage(String imagePath, ImageProcessingOptions options);
  Future<String?> processVideo(String videoPath, VideoProcessingOptions options);
  Future<String?> processAudio(String audioPath, AudioProcessingOptions options);
  Future<String?> generateImageThumbnail(String imagePath, Size thumbnailSize);
  Future<String?> generateVideoThumbnail(String videoPath, Size thumbnailSize);
  Future<Duration> getVideoDuration(String videoPath);
  Future<Duration> getAudioDuration(String audioPath);
  Future<String?> extractAudioFromVideo(String videoPath);
  Future<bool> validateMediaFile(String filePath, FileType type);
  void dispose();
}

// cache_service.dart
abstract class CacheService {
  Future<void> initialize();
  Future<void> put<T>(String key, T value, {Duration? expiry});
  Future<T?> get<T>(String key);
  Future<void> remove(String key);
  Future<void> clear();
  Future<void> clearExpired();
  Future<int> getCacheSize();
  Future<void> setMaxSize(int maxSizeInBytes);
  Future<void> preloadCache(List<String> keys);
  Stream<CacheEvent> get cacheEventStream;
  void dispose();
}

// contact_service.dart
abstract class ContactService {
  Future<void> initialize();
  Future<List<PhoneContact>> getPhoneContacts();
  Future<void> syncContacts();
  Future<List<Identity>> findRegisteredContacts();
  Future<void> inviteContact(String phoneNumber);
  Future<void> blockContact(String identityId);
  Future<void> unblockContact(String identityId);
  Future<List<Identity>> getBlockedContacts();
  Future<void> addToFavorites(String identityId);
  Future<void> removeFromFavorites(String identityId);
  Stream<ContactSyncStatus> get syncStatusStream;
  void dispose();
}

// presence_service.dart
abstract class PresenceService {
  Future<void> initialize();
  Future<void> updatePresence(PresenceStatus status);
  Future<void> startPresenceUpdates();
  Future<void> stopPresenceUpdates();
  Stream<Map<String, PresenceInfo>> get presenceStream;
  Future<PresenceInfo?> getPresence(String identityId);
  Future<void> setTypingIndicator(String roomId, bool isTyping);
  Stream<TypingIndicator> get typingIndicatorStream;
  void dispose();
}

// location_service.dart
abstract class LocationService {
  Future<void> initialize();
  Future<LocationData?> getCurrentLocation();
  Stream<LocationData> get locationStream;
  Future<void> startLocationTracking();
  Future<void> stopLocationTracking();
  Future<String?> getAddressFromCoordinates(double latitude, double longitude);
  Future<LocationData?> getLocationFromAddress(String address);
  Future<List<PlaceResult>> searchNearbyPlaces(double latitude, double longitude, String query);
  Future<bool> requestLocationPermission();
  void dispose();
}

// backup_service.dart
abstract class BackupService {
  Future<void> initialize();
  Future<String> createBackup({bool includeMedia = true});
  Future<void> restoreBackup(String backupPath);
  Future<void> uploadBackupToCloud(String backupPath);
  Future<String?> downloadBackupFromCloud(String backupId);
  Future<List<BackupInfo>> getCloudBackups();
  Future<void> deleteCloudBackup(String backupId);
  Future<void> scheduleAutoBackup(BackupFrequency frequency);
  Future<void> cancelAutoBackup();
  Stream<BackupProgress> get backupProgressStream;
  void dispose();
}

// analytics_service.dart
abstract class AnalyticsService {
  Future<void> initialize();
  Future<void> logEvent(String eventName, Map<String, dynamic> parameters);
  Future<void> setUserId(String userId);
  Future<void> setUserProperty(String name, String value);
  Future<void> logScreenView(String screenName);
  Future<void> logError(Exception error, StackTrace stackTrace);
  Future<void> logCustomEvent(CustomAnalyticsEvent event);
  Future<void> startSession();
  Future<void> endSession();
  Future<void> enableAnalytics(bool enabled);
  void dispose();
}

// settings_service.dart
abstract class SettingsService {
  Future<void> initialize();
  Future<AppSettings> getSettings();
  Future<void> updateSettings(AppSettings settings);
  Stream<AppSettings> get settingsStream;
  Future<void> updateTheme(ThemeMode theme);
  Future<void> updateLanguage(String languageCode);
  Future<void> updateFontSize(double fontSize);
  Future<void> resetSettings();
  Future<String> exportSettings();
  Future<void> importSettings(String settingsData);
  void dispose();
}

// permission_service.dart
abstract class PermissionService {
  Future<void> initialize();
  Future<PermissionResult> requestPermission(Permission permission);
  Future<Map<Permission, PermissionResult>> requestMultiplePermissions(List<Permission> permissions);
  Future<PermissionResult> checkPermission(Permission permission);
  Future<bool> shouldShowRationale(Permission permission);
  Future<void> openAppSettings();
  Stream<PermissionResult> watchPermission(Permission permission);
  void dispose();
}

// search_service.dart
abstract class SearchService {
  Future<void> initialize();
  Future<SearchResults> searchGlobal(String query);
  Future<List<Message>> searchMessages(String query, {String? roomId});
  Future<List<Room>> searchRooms(String query);
  Future<List<Identity>> searchContacts(String query);
  Future<void> indexContent(String id, String content, SearchType type);
  Future<void> removeFromIndex(String id, SearchType type);
  Future<void> clearIndex(SearchType type);
  Future<List<String>> getSearchSuggestions(String partialQuery);
  void dispose();
}

// voice_service.dart
abstract class VoiceService {
  Future<void> initialize();
  Future<bool> startRecording(String outputPath);
  Future<String?> stopRecording();
  Future<void> pauseRecording();
  Future<void> resumeRecording();
  Future<void> cancelRecording();
  Future<void> playAudio(String audioPath);
  Future<void> pauseAudio();
  Future<void> stopAudio();
  Stream<RecordingState> get recordingStateStream;
  Stream<AudioPlaybackState> get playbackStateStream;
  Future<Duration> getAudioDuration(String audioPath);
  void dispose();
}

// translation_service.dart
abstract class TranslationService {
  Future<void> initialize();
  Future<String> translateText(String text, String targetLanguage);
  Future<String> detectLanguage(String text);
  Future<List<String>> getSupportedLanguages();
  Future<void> downloadLanguagePack(String languageCode);
  Future<bool> isLanguagePackDownloaded(String languageCode);
  Future<String> translateMessage(String messageId, String targetLanguage);
  void dispose();
}

// theme_service.dart
abstract class ThemeService {
  Future<void> initialize();
  Future<void> setTheme(ThemeMode mode);
  ThemeMode getCurrentTheme();
  Stream<ThemeMode> get themeStream;
  Future<void> setCustomTheme(CustomTheme theme);
  Future<List<CustomTheme>> getAvailableThemes();
  Future<void> setAccentColor(Color color);
  Color getAccentColor();
  void dispose();
}


class WebSocketEvent {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  WebSocketEvent({required this.type, required this.data, required this.timestamp});
}

class AuthResult {
  final bool success;
  final String? error;
  final UserProfile? user;
  final String? token;
  AuthResult({required this.success, this.error, this.user, this.token});
}

class NotificationResponse {
  final String notificationId;
  final String? actionId;
  final Map<String, dynamic>? data;
  NotificationResponse({required this.notificationId, this.actionId, this.data});
}

class RemoteMessage {
  final String messageId;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  RemoteMessage({required this.messageId, required this.title, required this.body, required this.data});
}

class QueueItem {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final int retryCount;
  QueueItem({required this.id, required this.type, required this.data, required this.createdAt, this.retryCount = 0});
}

class RoomAction {
  final String type;
  final String roomId;
  final Map<String, dynamic> data;
  RoomAction({required this.type, required this.roomId, required this.data});
}

class UserAction {
  final String type;
  final String userId;
  final Map<String, dynamic> data;
  UserAction({required this.type, required this.userId, required this.data});
}

class KeyPair {
  final String publicKey;
  final String privateKey;
  KeyPair({required this.publicKey, required this.privateKey});
}

class ImageProcessingOptions {
  final int? quality;
  final Size? maxSize;
  final ImageFormat? format;
  ImageProcessingOptions({this.quality, this.maxSize, this.format});
}

class VideoProcessingOptions {
  final int? quality;
  final Size? maxSize;
  final Duration? maxDuration;
  VideoProcessingOptions({this.quality, this.maxSize, this.maxDuration});
}

class AudioProcessingOptions {
  final int? bitRate;
  final int? sampleRate;
  final AudioFormat? format;
  AudioProcessingOptions({this.bitRate, this.sampleRate, this.format});
}

class PhoneContact {
  final String name;
  final String phoneNumber;
  final String? email;
  PhoneContact({required this.name, required this.phoneNumber, this.email});
}

class PresenceInfo {
  final PresenceStatus status;
  final DateTime lastSeen;
  final String? customMessage;
  PresenceInfo({required this.status, required this.lastSeen, this.customMessage});
}

class TypingIndicator {
  final String roomId;
  final String userId;
  final bool isTyping;
  TypingIndicator({required this.roomId, required this.userId, required this.isTyping});
}

class LocationData {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final DateTime timestamp;
  LocationData({required this.latitude, required this.longitude, this.accuracy, required this.timestamp});
}

class PlaceResult {
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  PlaceResult({required this.name, required this.address, required this.latitude, required this.longitude});
}

class BackupInfo {
  final String id;
  final String name;
  final DateTime createdAt;
  final int size;
  final bool isCloud;
  BackupInfo({required this.id, required this.name, required this.createdAt, required this.size, this.isCloud = false});
}

class BackupProgress {
  final double progress;
  final String status;
  final String? currentItem;
  final String? error;
  BackupProgress({required this.progress, required this.status, this.currentItem, this.error});
}

class CustomAnalyticsEvent {
  final String name;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;
  CustomAnalyticsEvent({required this.name, required this.parameters, required this.timestamp});
}

class AppSettings {
  final ThemeMode theme;
  final String language;
  final double fontSize;
  final NotificationSettings notifications;
  final PrivacySettings privacy;
  final ChatSettings chat;
  AppSettings({
    required this.theme,
    required this.language,
    required this.fontSize,
    required this.notifications,
    required this.privacy,
    required this.chat,
  });
}

class NotificationSettings {
  final bool enabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool showPreview;
  NotificationSettings({
    required this.enabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.showPreview,
  });
}

class PrivacySettings {
  final bool showLastSeen;
  final bool showProfilePhoto;
  final bool allowReadReceipts;
  final bool allowTypingIndicator;
  PrivacySettings({
    required this.showLastSeen,
    required this.showProfilePhoto,
    required this.allowReadReceipts,
    required this.allowTypingIndicator,
  });
}

class ChatSettings {
  final bool enterToSend;
  final bool showTimestamps;
  final String wallpaper;
  final bool autoDownloadMedia;
  ChatSettings({
    required this.enterToSend,
    required this.showTimestamps,
    required this.wallpaper,
    required this.autoDownloadMedia,
  });
}

class SearchResults {
  final List<Message> messages;
  final List<Room> rooms;
  final List<Identity> contacts;
  final List<FileResult> files;
  SearchResults({
    required this.messages,
    required this.rooms,
    required this.contacts,
    required this.files,
  });
}

class FileResult {
  final String id;
  final String name;
  final String path;
  final FileType type;
  FileResult({required this.id, required this.name, required this.path, required this.type});
}



class CacheEvent {
  final String type;
  final String key;
  final dynamic value;
  final DateTime timestamp;
  CacheEvent({required this.type, required this.key, this.value, required this.timestamp});
}

class Size {
  final int width;
  final int height;
  Size({required this.width, required this.height});
}

class Color {
  final int value;
  Color(this.value);
}

