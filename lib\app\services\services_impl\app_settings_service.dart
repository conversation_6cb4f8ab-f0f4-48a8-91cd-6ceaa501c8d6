import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/models/entities/app_settings_model.dart';
import '../../data/models/entities/identity_settings_model.dart';
import '../services_interface/app_services_interface.dart';
import '../services_interface/settings_service_interface.dart'
    as settings_interface;
import 'objectbox_service.dart';

/// Enhanced Settings service implementation
/// Manages both app-global and identity-specific settings with ObjectBox persistence
class EnhancedSettingsService extends InitializableService
    implements settings_interface.SettingsServiceAbstract {
  // ========================================
  // PRIVATE FIELDS
  // ========================================

  late AppSettings _appSettings;
  final Map<String, IdentitySettings> _identitySettingsCache = {};
  final Map<String, StreamController<IdentitySettings>> _identityStreams = {};

  // Stream controllers for change notifications
  final StreamController<AppSettings> _appSettingsController =
      StreamController<AppSettings>.broadcast();
  final StreamController<settings_interface.SettingsChangeEvent>
  _settingsChangeController =
      StreamController<settings_interface.SettingsChangeEvent>.broadcast();
  final Map<String, List<Function(dynamic)>> _settingListeners = {};

  ObjectboxService get _objectboxService => Get.find<ObjectboxService>();

  // ========================================
  // SERVICE LIFECYCLE
  // ========================================

  @override
  Future<void> _init() async {
    await loadAppSettings();
  }

  @override
  bool get isHealthy => super.isHealthy && _objectboxService.isReady;

  Future<void> disposeResources() async {
    await _appSettingsController.close();
    await _settingsChangeController.close();
    for (final controller in _identityStreams.values) {
      await controller.close();
    }
    _identityStreams.clear();
    _identitySettingsCache.clear();
    _settingListeners.clear();
  }

  // ========================================
  // APP-GLOBAL SETTINGS OPERATIONS
  // ========================================

  @override
  Future<AppSettings> getAppSettings() async {
    return _appSettings;
  }

  @override
  Future<void> updateAppSettings(AppSettings settings) async {
    final oldSettings = _appSettings;
    _appSettings = settings;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifyAppSettingsChange('appSettings', oldSettings, _appSettings);
  }

  @override
  Stream<AppSettings> get appSettingsStream => _appSettingsController.stream;

  @override
  Future<void> loadAppSettings() async {
    try {
      _appSettings =
          _objectboxService.appSettingsBox.get(1) ?? AppSettings.defaults();
    } catch (e) {
      _appSettings = AppSettings.defaults();
    }
  }

  @override
  Future<void> saveAppSettings() async {
    try {
      _objectboxService.appSettingsBox.put(_appSettings);
    } catch (e) {
      throw Exception('Failed to save app settings: $e');
    }
  }

  @override
  Future<void> resetAppSettings() async {
    _appSettings = AppSettings.defaults();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
  }

  // ========================================
  // IDENTITY-SPECIFIC SETTINGS OPERATIONS
  // ========================================

  @override
  Future<IdentitySettings> getIdentitySettings(String myIdentityCardId) async {
    // Check cache first
    if (_identitySettingsCache.containsKey(myIdentityCardId)) {
      return _identitySettingsCache[myIdentityCardId]!;
    }

    try {
      // TODO: Implement proper ObjectBox query when schema is ready
      // For now, return default settings
      return await createIdentitySettings(myIdentityCardId);
    } catch (e) {
      // Return default settings on error
      return await createIdentitySettings(myIdentityCardId);
    }
  }

  @override
  Future<void> updateIdentitySettings(IdentitySettings settings) async {
    final oldSettings = _identitySettingsCache[settings.myIdentityCardId];
    settings.touch();

    try {
      _objectboxService.identitySettingsBox?.put(settings);
      _identitySettingsCache[settings.myIdentityCardId] = settings;

      // Notify stream listeners
      if (_identityStreams.containsKey(settings.myIdentityCardId)) {
        _identityStreams[settings.myIdentityCardId]!.add(settings);
      }

      _notifyIdentitySettingsChange(
        settings.myIdentityCardId,
        'identitySettings',
        oldSettings,
        settings,
      );
    } catch (e) {
      throw Exception('Failed to save identity settings: $e');
    }
  }

  @override
  Stream<IdentitySettings> getIdentitySettingsStream(String myIdentityCardId) {
    if (!_identityStreams.containsKey(myIdentityCardId)) {
      _identityStreams[myIdentityCardId] =
          StreamController<IdentitySettings>.broadcast();
    }
    return _identityStreams[myIdentityCardId]!.stream;
  }

  @override
  Future<List<IdentitySettings>> getAllIdentitySettings() async {
    try {
      final allSettings = _objectboxService.identitySettingsBox?.getAll() ?? [];

      // Update cache
      for (final settings in allSettings) {
        _identitySettingsCache[settings.myIdentityCardId] = settings;
      }

      return allSettings;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<IdentitySettings> createIdentitySettings(
    String myIdentityCardId, {
    bool isBusinessIdentity = false,
  }) async {
    final settings = IdentitySettings.defaults(
      myIdentityCardId,
      isBusinessIdentity: isBusinessIdentity,
    );

    try {
      _objectboxService.identitySettingsBox?.put(settings);
      _identitySettingsCache[myIdentityCardId] = settings;
      return settings;
    } catch (e) {
      throw Exception('Failed to create identity settings: $e');
    }
  }

  @override
  Future<void> deleteIdentitySettings(String myIdentityCardId) async {
    try {
      // TODO: Implement proper ObjectBox deletion when schema is ready

      // Clean up cache and streams
      _identitySettingsCache.remove(myIdentityCardId);
      if (_identityStreams.containsKey(myIdentityCardId)) {
        await _identityStreams[myIdentityCardId]!.close();
        _identityStreams.remove(myIdentityCardId);
      }
    } catch (e) {
      throw Exception('Failed to delete identity settings: $e');
    }
  }

  @override
  Future<void> resetIdentitySettings(String myIdentityCardId) async {
    final currentSettings = await getIdentitySettings(myIdentityCardId);
    final newSettings = IdentitySettings.defaults(
      myIdentityCardId,
      isBusinessIdentity: currentSettings.isBusinessIdentity ?? false,
    );
    await updateIdentitySettings(newSettings);
  }

  // ========================================
  // THEME AND APPEARANCE SETTINGS
  // ========================================

  @override
  bool get isDarkMode => _appSettings.isDarkMode ?? false;

  @override
  Future<void> setDarkMode(bool enabled) async {
    _appSettings.isDarkMode = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('isDarkMode', enabled);
  }

  @override
  ThemeMode get themeMode => isDarkMode ? ThemeMode.dark : ThemeMode.light;

  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    switch (mode) {
      case ThemeMode.light:
        await setDarkMode(false);
        break;
      case ThemeMode.dark:
        await setDarkMode(true);
        break;
      case ThemeMode.system:
        _appSettings.useSystemTheme = true;
        await saveAppSettings();
        break;
    }
  }

  double get fontSize => _appSettings.fontSize ?? 14.0;

  Future<void> setFontSize(double size) async {
    _appSettings.fontSize = size;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('fontSize', size);
  }

  String? get language => _appSettings.language;

  Future<void> setLanguage(String languageCode) async {
    _appSettings.language = languageCode;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('language', languageCode);
  }

  // ========================================
  // NOTIFICATION SETTINGS
  // ========================================

  @override
  bool get notificationsEnabled => _appSettings.notificationEnabled ?? true;

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    _appSettings.notificationEnabled = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('notificationsEnabled', enabled);
  }

  bool get soundEnabled => _appSettings.soundEnabled ?? true;

  Future<void> setSoundEnabled(bool enabled) async {
    _appSettings.soundEnabled = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('soundEnabled', enabled);
  }

  bool get vibrationEnabled => _appSettings.vibrationEnabled ?? true;

  Future<void> setVibrationEnabled(bool enabled) async {
    _appSettings.vibrationEnabled = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('vibrationEnabled', enabled);
  }

  // ========================================
  // PRIVACY AND SECURITY SETTINGS
  // ========================================

  @override
  bool get analyticsEnabled => _appSettings.analyticsEnabled ?? true;

  @override
  Future<void> setAnalyticsEnabled(bool enabled) async {
    _appSettings.analyticsEnabled = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('analyticsEnabled', enabled);
  }

  @override
  bool get crashReportingEnabled => _appSettings.crashReportingEnabled ?? true;

  @override
  Future<void> setCrashReportingEnabled(bool enabled) async {
    _appSettings.crashReportingEnabled = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('crashReportingEnabled', enabled);
  }

  @override
  bool get backgroundWorkEnabled => _appSettings.backgroundWorkEnabled ?? true;

  @override
  Future<void> setBackgroundWorkEnabled(bool enabled) async {
    _appSettings.backgroundWorkEnabled = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _appSettingsController.add(_appSettings);
    _notifySettingChange('backgroundWorkEnabled', enabled);
  }

  // ========================================
  // HELPER METHODS
  // ========================================

  void _notifySettingChange(String key, dynamic value) {
    _settingListeners[key]?.forEach((listener) => listener(value));
    _settingsChangeController.add(
      settings_interface.SettingsChangeEvent(
        type: settings_interface.SettingsChangeType.appGlobal,
        settingKey: key,
        oldValue: null, // Could be enhanced to track old values
        newValue: value,
        timestamp: DateTime.now(),
      ),
    );
  }

  void _notifyAppSettingsChange(
    String key,
    AppSettings oldValue,
    AppSettings newValue,
  ) {
    _settingsChangeController.add(
      settings_interface.SettingsChangeEvent(
        type: settings_interface.SettingsChangeType.appGlobal,
        settingKey: key,
        oldValue: oldValue,
        newValue: newValue,
        timestamp: DateTime.now(),
      ),
    );
  }

  void _notifyIdentitySettingsChange(
    String identityId,
    String key,
    IdentitySettings? oldValue,
    IdentitySettings newValue,
  ) {
    _settingsChangeController.add(
      settings_interface.SettingsChangeEvent(
        type: settings_interface.SettingsChangeType.identity,
        settingKey: key,
        oldValue: oldValue,
        newValue: newValue,
        timestamp: DateTime.now(),
        identityId: identityId,
      ),
    );
  }

  // ========================================
  // REMAINING INTERFACE IMPLEMENTATIONS
  // ========================================

  @override
  bool get pushNotificationsEnabled => _appSettings.notificationEnabled ?? true;

  @override
  Future<void> setPushNotificationsEnabled(bool enabled) async {
    await setNotificationsEnabled(enabled);
  }

  @override
  bool get autoSaveEnabled => _appSettings.autoSaveEnabled ?? true;

  @override
  Future<void> setAutoSaveEnabled(bool enabled) async {
    _appSettings.autoSaveEnabled = enabled;
    _appSettings.touch();
    await saveAppSettings();
    _notifySettingChange('autoSaveEnabled', enabled);
  }

  @override
  Duration get autoSaveInterval =>
      Duration(minutes: _appSettings.autoSaveIntervalMinutes ?? 5);

  @override
  Future<void> setAutoSaveInterval(Duration interval) async {
    _appSettings.autoSaveIntervalMinutes = interval.inMinutes;
    _appSettings.touch();
    await saveAppSettings();
    _notifySettingChange('autoSaveInterval', interval);
  }

  @override
  T? getSetting<T>(String key, {T? defaultValue}) {
    // This is a simplified implementation - could be enhanced with reflection
    switch (key) {
      case 'isDarkMode':
        return isDarkMode as T?;
      case 'fontSize':
        return fontSize as T?;
      case 'language':
        return language as T?;
      case 'notificationsEnabled':
        return notificationsEnabled as T?;
      default:
        return defaultValue;
    }
  }

  @override
  Future<void> setSetting<T>(String key, T value) async {
    switch (key) {
      case 'isDarkMode':
        if (value is bool) await setDarkMode(value);
        break;
      case 'fontSize':
        if (value is double) await setFontSize(value);
        break;
      case 'language':
        if (value is String) await setLanguage(value);
        break;
      case 'notificationsEnabled':
        if (value is bool) await setNotificationsEnabled(value);
        break;
    }
  }

  @override
  Future<void> removeSetting(String key) async {
    // Implementation depends on specific requirements
  }

  @override
  bool hasSetting(String key) {
    return getSetting(key) != null;
  }

  @override
  Map<String, dynamic> getAllSettings() {
    return {
      'isDarkMode': isDarkMode,
      'fontSize': fontSize,
      'language': language,
      'notificationsEnabled': notificationsEnabled,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'analyticsEnabled': analyticsEnabled,
      'crashReportingEnabled': crashReportingEnabled,
      'backgroundWorkEnabled': backgroundWorkEnabled,
      'autoSaveEnabled': autoSaveEnabled,
      'autoSaveInterval': autoSaveInterval.inMinutes,
    };
  }

  @override
  Future<void> setMultipleSettings(Map<String, dynamic> settings) async {
    for (final entry in settings.entries) {
      await setSetting(entry.key, entry.value);
    }
  }

  @override
  Future<void> clearAllSettings() async {
    await resetAppSettings();
  }

  @override
  bool validateSettings() {
    // Basic validation - can be extended
    return true;
  }

  @override
  List<String> getInvalidSettings() {
    // Return list of invalid setting keys
    return [];
  }

  @override
  Future<String> exportSettings() async {
    return await exportAllSettings();
  }

  @override
  Future<bool> importSettings(String settingsJson) async {
    try {
      await importAllSettings(settingsJson);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Stream<Map<String, dynamic>> get settingsChanges => _settingsChangeController
      .stream
      .map((event) => {event.settingKey: event.newValue});

  @override
  void addSettingChangeListener(String key, Function(dynamic value) listener) {
    _settingListeners.putIfAbsent(key, () => []).add(listener);
  }

  @override
  void removeSettingChangeListener(
    String key,
    Function(dynamic value) listener,
  ) {
    _settingListeners[key]?.remove(listener);
    if (_settingListeners[key]?.isEmpty == true) {
      _settingListeners.remove(key);
    }
  }

  @override
  Future<String> exportAllSettings() async {
    final data = {
      'appSettings': _appSettings,
      'identitySettings': await getAllIdentitySettings(),
      'exportedAt': DateTime.now().toIso8601String(),
      'version': 1,
    };
    return jsonEncode(data);
  }

  @override
  Future<String> exportAppSettings() async {
    final data = {
      'appSettings': _appSettings,
      'exportedAt': DateTime.now().toIso8601String(),
      'version': 1,
    };
    return jsonEncode(data);
  }

  @override
  Future<String> exportIdentitySettings(String myIdentityCardId) async {
    final settings = await getIdentitySettings(myIdentityCardId);
    final data = {
      'identitySettings': settings,
      'myIdentityCardId': myIdentityCardId,
      'exportedAt': DateTime.now().toIso8601String(),
      'version': 1,
    };
    return jsonEncode(data);
  }

  @override
  Future<void> importAllSettings(String settingsData) async {
    try {
      final data = jsonDecode(settingsData) as Map<String, dynamic>;

      // Import app settings
      if (data.containsKey('appSettings')) {
        // This would need proper deserialization logic
        await resetAppSettings(); // Placeholder
      }

      // Import identity settings
      if (data.containsKey('identitySettings')) {
        // This would need proper deserialization logic
      }
    } catch (e) {
      throw Exception('Failed to import settings: $e');
    }
  }

  @override
  Future<bool> validateAllSettings(String settingsData) async {
    try {
      final data = jsonDecode(settingsData) as Map<String, dynamic>;
      return data.containsKey('version') && data['version'] == 1;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<settings_interface.SettingsMetadata> getSettingsMetadata() async {
    final identitySettings = await getAllIdentitySettings();
    return settings_interface.SettingsMetadata(
      lastModified: _appSettings.lastModified ?? DateTime.now(),
      createdAt: _appSettings.createdAt ?? DateTime.now(),
      appSettingsVersion: _appSettings.settingsVersion ?? 1,
      identitySettingsVersion: 1,
      deviceId: _appSettings.deviceName,
      totalIdentities: identitySettings.length,
    );
  }
}
