import 'app_services_interface.dart';

/// Interface for ObjectBox database operations
abstract class ObjectboxServiceAbstract extends InitializableServiceAbstract {
  /// Get the ObjectBox instance
  dynamic get objectbox;

  /// Initialize ObjectBox database
  Future<void> initializeDatabase();

  /// Close the database connection
  Future<void> closeDatabase();

  /// Check if database is open and ready
  bool get isDatabaseReady;

  /// Get app box for general app data and settings
  dynamic get appSettingsBox;

  /// Backup database
  Future<bool> backupDatabase(String path);

  /// Restore database from backup
  Future<bool> restoreDatabase(String path);

  /// Get database statistics
  Map<String, dynamic> getDatabaseStats();
}
