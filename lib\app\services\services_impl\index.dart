/// Service Implementations Export File
///
/// This file provides a single import point for all service implementations
/// in the application. It promotes clean architecture by providing concrete
/// implementations of the service interfaces.

// Note: Base implementation classes are now in the interfaces folder
// to maintain proper separation between interfaces and implementations

// ========================================
// INDIVIDUAL SERVICE IMPLEMENTATIONS
// ========================================

/// Database service implementation
export 'objectbox_service.dart';

/// Settings management implementation
export 'settings_service.dart';

/// Localization service implementation
export 'localization_service.dart';

// ========================================
// SERVICE MANAGEMENT IMPLEMENTATIONS
// ========================================

/// Service initialization implementation
export 'service_initializer_impl.dart';

// ========================================
// LEGACY EXPORTS (for backward compatibility)
// ========================================

/// Legacy combined export file
export 'appservices.dart';

// ========================================
// CONVENIENCE FUNCTIONS
// ========================================

/// Convenience function for service initialization
export 'service_initializer_impl.dart' show initializeAppServices;
