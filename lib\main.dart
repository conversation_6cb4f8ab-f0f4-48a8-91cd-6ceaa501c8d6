import 'package:deewan/app/bindings/initial_bindings.dart';
import 'package:deewan/core/localization/translation.dart';
import 'package:deewan/app/services/services_impl/localization_service.dart';
import 'package:deewan/core/theme/app_theme.dart';
import 'package:deewan/app/routes/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Services are now initialized lazily via InitialBindings
  // No need for manual service initialization

  runApp(const Deewan());
}

// ignore: must_be_immutable

class Deewan extends StatelessWidget {
  const Deewan({super.key});

  @override
  Widget build(BuildContext context) {
    // Get services
    return Obx(
      () => GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'title'.tr,

        // Theme configuration with RTL support
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: .isDarkMode
            ? ThemeMode.dark
            : ThemeMode.light,

        // Localization configuration
        translations: AppTranslation(),
        locale: LocalizationService.currentLocale,
        fallbackLocale: LocalizationService.fallbackLocale,
        supportedLocales: LocalizationService.supportedLocales,

        // Flutter's built-in localizations
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],

        // RTL support
        builder: (context, child) {
          return Directionality(
            textDirection: localizationService.textDirection,
            child: child!,
          );
        },

        // Navigation configuration
        initialRoute: routes?[0].name,
        getPages: routes,
        defaultTransition: Transition.fade,
        binds: Initialbindings().dependencies(),
      ),
    );
  }
}
