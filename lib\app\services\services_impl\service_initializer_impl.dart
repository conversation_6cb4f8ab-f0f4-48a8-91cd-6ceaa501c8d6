import 'package:get/get.dart';
import 'localization_service.dart';
import '../services_interface/app_services_interface.dart' as app_services;
import '../services_interface/service_initializer_interface.dart'
    as service_init;
import '../services_interface/objectbox_service_interface.dart'
    as objectbox_interface;
import '../services_interface/settings_service_interface.dart'
    as settings_interface;
import '../services_interface/localization_service_interface.dart'
    as localization_interface;
import 'appservices.dart';

/// Implementation of service initializer
class ServiceInitializer implements service_init.IServiceInitializer {
  final List<Exception> _initializationErrors = [];
  final Map<Type, bool> _serviceStatus = {};
  final Set<Type> _failedServices = {};

  @override
  Future<void> initializeAllServices() async {
    print('Starting service initialization...');

    try {
      // Initialize services in dependency order
      for (final serviceType in initializationOrder) {
        await _initializeServiceByType(serviceType);
      }

      print('All services initialized successfully.');
    } catch (e) {
      print('Service initialization failed: $e');
      _initializationErrors.add(Exception('Service initialization failed: $e'));
      rethrow;
    }
  }

  @override
  Future<T> initializeService<
    T extends app_services.InitializableServiceAbstract
  >() async {
    final service = getService<T>();
    await service.ensureInitialized();
    _serviceStatus[T] = true;
    return service;
  }

  @override
  List<Type> get initializationOrder => [
    ObjectboxService,
    LocalizationService,
    SettingsService,
  ];

  @override
  bool get allServicesInitialized {
    return initializationOrder.every((type) => _serviceStatus[type] == true);
  }

  @override
  bool
  isServiceInitialized<T extends app_services.InitializableServiceAbstract>() {
    return _serviceStatus[T] == true;
  }

  @override
  T getService<T extends app_services.InitializableServiceAbstract>() {
    return Get.find<T>();
  }

  @override
  void registerService<T extends app_services.InitializableServiceAbstract>(
    T service,
  ) {
    Get.put<T>(service, permanent: true);
  }

  @override
  void
  unregisterService<T extends app_services.InitializableServiceAbstract>() {
    Get.delete<T>();
    _serviceStatus[T] = false;
  }

  @override
  Future<void> startServices() async {
    await initializeAllServices();
  }

  @override
  Future<void> stopServices() async {
    // Stop services in reverse order
    for (final serviceType in initializationOrder.reversed) {
      try {
        final service = Get.find(tag: serviceType.toString());
        if (service is app_services.InitializableServiceAbstract) {
          // Perform cleanup if needed
        }
      } catch (e) {
        // Service not found or already stopped
      }
    }
  }

  @override
  Future<void> restartServices() async {
    await stopServices();
    await startServices();
  }

  @override
  bool
  checkServiceHealth<T extends app_services.InitializableServiceAbstract>() {
    try {
      final service = getService<T>();
      return service.isReady;
    } catch (e) {
      return false;
    }
  }

  @override
  Map<Type, bool> checkAllServicesHealth() {
    final healthStatus = <Type, bool>{};
    for (final serviceType in initializationOrder) {
      try {
        final service = Get.find(tag: serviceType.toString());
        if (service is app_services.InitializableServiceAbstract) {
          healthStatus[serviceType] = service.isReady;
        } else {
          healthStatus[serviceType] = false;
        }
      } catch (e) {
        healthStatus[serviceType] = false;
      }
    }
    return healthStatus;
  }

  @override
  List<Type> getServiceDependencies<
    T extends app_services.InitializableServiceAbstract
  >() {
    // Define service dependencies
    if (T == LocalizationService) {
      return [ObjectboxService];
    } else if (T == SettingsService) {
      return [ObjectboxService];
    }
    return [];
  }

  @override
  bool areDependenciesSatisfied<
    T extends app_services.InitializableServiceAbstract
  >() {
    final dependencies = getServiceDependencies<T>();
    return dependencies.every((dep) => _serviceStatus[dep] == true);
  }

  @override
  void onServiceInitializationError<
    T extends app_services.InitializableServiceAbstract
  >(Exception error) {
    _initializationErrors.add(error);
    _failedServices.add(T);
    print('Service initialization error for ${T.toString()}: $error');
  }

  @override
  List<Exception> get initializationErrors =>
      List.unmodifiable(_initializationErrors);

  @override
  List<Type> get failedServices => List.unmodifiable(_failedServices);

  @override
  void clearErrors() {
    _initializationErrors.clear();
    _failedServices.clear();
  }

  /// Private helper method to initialize service by type
  Future<void> _initializeServiceByType(Type serviceType) async {
    try {
      if (serviceType == ObjectboxService) {
        final service = ObjectboxService();
        await service.ensureInitialized();
        registerService<ObjectboxService>(service);
        _serviceStatus[serviceType] = true;
      } else if (serviceType == LocalizationService) {
        final service = LocalizationService();
        await service.ensureInitialized();
        registerService<LocalizationService>(service);
        _serviceStatus[serviceType] = true;
      } else if (serviceType == SettingsService) {
        final service = SettingsService();
        await service.ensureInitialized();
        registerService<SettingsService>(service);
        _serviceStatus[serviceType] = true;
      }
    } catch (e) {
      onServiceInitializationError<app_services.InitializableServiceAbstract>(
        Exception('Failed to initialize $serviceType: $e'),
      );
      rethrow;
    }
  }
}

/// Service registry implementation
class ServiceRegistry implements service_init.IServiceRegistry {
  final Map<Type, app_services.InitializableServiceAbstract> _services = {};

  @override
  void register<T extends app_services.InitializableServiceAbstract>(
    T service,
  ) {
    _services[T] = service;
    Get.put<T>(service, permanent: true);
  }

  @override
  void unregister<T extends app_services.InitializableServiceAbstract>() {
    _services.remove(T);
    Get.delete<T>();
  }

  @override
  T get<T extends app_services.InitializableServiceAbstract>() {
    final service = _services[T];
    if (service == null) {
      throw Exception('Service of type $T not found');
    }
    return service as T;
  }

  @override
  bool isRegistered<T extends app_services.InitializableServiceAbstract>() {
    return _services.containsKey(T);
  }

  @override
  List<app_services.InitializableServiceAbstract> getAllServices() {
    return List.unmodifiable(_services.values);
  }

  @override
  void clear() {
    for (final serviceType in _services.keys) {
      Get.delete(tag: serviceType.toString());
    }
    _services.clear();
  }
}

/// Service factory implementation
class ServiceFactory implements service_init.IServiceFactory {
  @override
  objectbox_interface.IObjectboxService createObjectboxService() {
    return ObjectboxService();
  }

  @override
  settings_interface.ISettingsService createSettingsService() {
    return SettingsService();
  }

  @override
  localization_interface.ILocalizationService createLocalizationService() {
    return LocalizationService();
  }

  @override
  T createService<T extends app_services.InitializableServiceAbstract>() {
    if (T == ObjectboxService) {
      return ObjectboxService() as T;
    } else if (T == SettingsService) {
      return SettingsService() as T;
    } else if (T == LocalizationService) {
      return LocalizationService() as T;
    }
    throw Exception('Unknown service type: $T');
  }
}

/// Convenience function to initialize all services
Future<void> initializeAppServices() async {
  final initializer = ServiceInitializer();
  await initializer.initializeAllServices();
}
