// Minimal ObjectBox mock for compilation
// This is a placeholder until the actual ObjectBox implementation is added

import '../../../app/data/models/entities/sittings_model.dart';

class ObjectBox {
  static Future<ObjectBox> create() async {
    return ObjectBox._();
  }

  ObjectBox._();

  // Mock box for app settings
  final AppBox appBox = AppBox();

  // Add any required methods here as needed
}

// Mock box implementation for Sittings
class AppBox {
  final Map<int, Settings> _storage = {};

  Settings? get(int id) {
    return _storage[id];
  }

  void put(Settings settings) {
    _storage[settings.id] = settings;
  }

  bool remove(int id) {
    return _storage.remove(id) != null;
  }

  List<Settings> getAll() {
    return _storage.values.toList();
  }

  void clear() {
    _storage.clear();
  }
}
